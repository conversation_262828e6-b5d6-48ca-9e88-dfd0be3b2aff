from utils.daz_parser import parse_daz_file
from utils.plane_calibrator import calibrate_plane
from utils.pillars_detector import detect_pillars
from utils.pillars_measurer import measure_pillar
import glob
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

files_to_process = glob.glob("raw_data/Canon/*.daz", recursive=True)

for filepath in files_to_process:
    height_matrix, x_step, y_step = parse_daz_file(filepath)
    calibrated_matrix, calibration_metadata = calibrate_plane(height_matrix)
    pillars_info = detect_pillars(calibrated_matrix, x_step, y_step)

    print(f"\n文件: {filepath}")
    print(f"检测到 {len(pillars_info)} 个柱子")
    print("-" * 80)

    # 为每个柱子创建详细的测量可视化
    for pillar_idx, pillar in enumerate(pillars_info):
        # 测量柱子尺寸
        meas_result = measure_pillar(pillar, calibrated_matrix, x_step, y_step)

        # 创建三列布局的图形
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(20, 6))

        # === 左图：校准后矩阵 + 当前柱子高亮 ===
        im1 = ax1.imshow(calibrated_matrix, cmap='viridis', aspect='equal', origin='lower')
        ax1.set_title(f'{filepath}')
        ax1.set_xlabel(f'X方向 (步长: {x_step:.4f} μm)')
        ax1.set_ylabel(f'Y方向 (步长: {y_step:.4f} μm)')
        plt.colorbar(im1, ax=ax1, label='校准后高度 (μm)')

        # 绘制所有柱子（淡色）
        for other_pillar in pillars_info:
            if other_pillar['pillar_id'] != pillar['pillar_id']:
                center_row, center_col = other_pillar['center']
                bbox = other_pillar['bbox']
                top_row, left_col, bottom_row, right_col = bbox
                width = right_col - left_col
                height = bottom_row - top_row

                rect = patches.Rectangle(
                    (left_col, top_row), width, height,
                    linewidth=1, edgecolor='gray', facecolor='none', alpha=0.5
                )
                ax1.add_patch(rect)

        # 高亮当前柱子
        center_row, center_col = pillar['center']
        bbox = pillar['bbox']
        top_row, left_col, bottom_row, right_col = bbox
        width = right_col - left_col
        height = bottom_row - top_row

        # 绘制当前柱子的边界框
        rect = patches.Rectangle(
            (left_col, top_row), width, height,
            linewidth=2, edgecolor='red', facecolor='none', alpha=0.8
        )
        ax1.add_patch(rect)

        # 绘制十字截面线
        ax1.axhline(y=center_row, color='yellow', linestyle='--', alpha=0.8, linewidth=2)
        ax1.axvline(x=center_col, color='yellow', linestyle='--', alpha=0.8, linewidth=2)

        # 标注中心点和标签
        ax1.plot(center_col, center_row, 'r+', markersize=10, markeredgewidth=2)
        ax1.annotate(
            pillar['pillar_id'],
            (center_col, center_row),
            xytext=(10, 10), textcoords='offset points',
            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9),
            fontsize=12, color='red', weight='bold'
        )

        # === 中图：X方向截面 ===
        # 提取水平截面数据
        horizontal_slice = calibrated_matrix[center_row, left_col:right_col + 1]
        horizontal_coords = np.arange(left_col, right_col + 1)

        ax2.plot(horizontal_coords, horizontal_slice, 'b-', linewidth=2, label='高度轮廓')
        ax2.set_title(f'X方向截面 - {pillar["pillar_id"]}')
        ax2.set_xlabel('X坐标 (像素)')
        ax2.set_ylabel('高度 (μm)')
        ax2.grid(True, alpha=0.3)


        ax2.axhline(y=meas_result['x_max_height'], color='black', linestyle='--', alpha=0.7,
                    label=f'最大高度 ({meas_result["x_max_height"]:.2f} μm)')
        # 绘制顶部阈值线和边界
        ax2.axhline(y=meas_result['x_top_height'], color='red', linestyle='-', alpha=0.7,
                    label=f'顶部阈值 ({meas_result["x_top_height"]:.2f} μm)')
        x_top_left, x_top_right = meas_result['x_top_border']
        ax2.axvline(x=x_top_left, color='red', linestyle=':', alpha=0.8)
        ax2.axvline(x=x_top_right, color='red', linestyle=':', alpha=0.8)
        ax2.annotate('', xy=(x_top_right, meas_result['x_top_height']),
                     xytext=(x_top_left, meas_result['x_top_height']),
                     arrowprops=dict(arrowstyle='<->', color='red', lw=2))
        ax2.text((x_top_left + x_top_right) / 2,
                 meas_result['x_top_height'] + (np.max(horizontal_slice) - meas_result['x_top_height']) * 0.1,
                 f'顶部宽度: {meas_result["width_x_top"]:.2f} μm',
                 ha='center', va='bottom', color='red', fontweight='bold', fontsize=10)

        # 绘制底部阈值线和边界
        ax2.axhline(y=meas_result['x_bottom_height'], color='orange', linestyle='-', alpha=0.7,
                    label=f'底部阈值 ({meas_result["x_bottom_height"]:.2f} μm)')
        x_bottom_left, x_bottom_right = meas_result['x_bottom_border']
        ax2.axvline(x=x_bottom_left, color='orange', linestyle=':', alpha=0.8)
        ax2.axvline(x=x_bottom_right, color='orange', linestyle=':', alpha=0.8)
        ax2.annotate('', xy=(x_bottom_right, meas_result['x_bottom_height']),
                     xytext=(x_bottom_left, meas_result['x_bottom_height']),
                     arrowprops=dict(arrowstyle='<->', color='orange', lw=2))
        ax2.text((x_bottom_left + x_bottom_right) / 2,
                 meas_result['x_bottom_height'] - (meas_result['x_bottom_height'] - np.min(horizontal_slice)) * 0.2,
                 f'底部宽度: {meas_result["width_x_bottom"]:.2f} μm',
                 ha='center', va='top', color='orange', fontweight='bold', fontsize=10)

        ax2.legend()

        # === 右图：Y方向截面 ===
        # 提取垂直截面数据
        vertical_slice = calibrated_matrix[top_row:bottom_row + 1, center_col]
        vertical_coords = np.arange(top_row, bottom_row + 1)

        ax3.plot(vertical_coords, vertical_slice, 'g-', linewidth=2, label='高度轮廓')
        ax3.set_title(f'Y方向截面 - {pillar["pillar_id"]}')
        ax3.set_xlabel('Y坐标 (像素)')
        ax3.set_ylabel('高度 (μm)')
        ax3.grid(True, alpha=0.3)

        ax3.axhline(y=meas_result['y_max_height'], color='black', linestyle='--', alpha=0.7,
                    label=f'最大高度 ({meas_result["y_max_height"]:.2f} μm)')
        # 绘制顶部阈值线和边界
        ax3.axhline(y=meas_result['y_top_height'], color='red', linestyle='-', alpha=0.7,
                    label=f'顶部阈值 ({meas_result["y_top_height"]:.2f} μm)')
        y_top_left, y_top_right = meas_result['y_top_border']
        ax3.axvline(x=y_top_left, color='red', linestyle=':', alpha=0.8)
        ax3.axvline(x=y_top_right, color='red', linestyle=':', alpha=0.8)
        ax3.annotate('', xy=(y_top_right, meas_result['y_top_height']),
                     xytext=(y_top_left, meas_result['y_top_height']),
                     arrowprops=dict(arrowstyle='<->', color='red', lw=2))
        ax3.text((y_top_left + y_top_right) / 2,
                 meas_result['y_top_height'] + (np.max(vertical_slice) - meas_result['y_top_height']) * 0.1,
                 f'顶部宽度: {meas_result["width_y_top"]:.2f} μm',
                 ha='center', va='bottom', color='red', fontweight='bold', fontsize=10)

        # 绘制底部阈值线和边界
        ax3.axhline(y=meas_result['y_bottom_height'], color='orange', linestyle='-', alpha=0.7,
                    label=f'底部阈值 ({meas_result["y_bottom_height"]:.2f} μm)')
        y_bottom_left, y_bottom_right = meas_result['y_bottom_border']
        ax3.axvline(x=y_bottom_left, color='orange', linestyle=':', alpha=0.8)
        ax3.axvline(x=y_bottom_right, color='orange', linestyle=':', alpha=0.8)
        ax3.annotate('', xy=(y_bottom_right, meas_result['y_bottom_height']),
                     xytext=(y_bottom_left, meas_result['y_bottom_height']),
                     arrowprops=dict(arrowstyle='<->', color='orange', lw=2))
        ax3.text((y_bottom_left + y_bottom_right) / 2,
                 meas_result['y_bottom_height'] - (meas_result['y_bottom_height'] - np.min(vertical_slice)) * 0.2,
                 f'底部宽度: {meas_result["width_y_bottom"]:.2f} μm',
                 ha='center', va='top', color='orange', fontweight='bold', fontsize=10)

        ax3.legend()

        plt.tight_layout()
        plt.show()

        # 打印详细测量结果
        print(f"\n柱子 {pillar['pillar_id']} 测量结果:")
        print(f"  中心坐标: ({center_row}, {center_col})")
        print(f"  总高度: {meas_result['height']:.2f} μm")
        print(f"  X方向:")
        print(
            f"    顶部宽度: {meas_result['width_x_top']:.2f} μm (边界: {meas_result['x_top_border'][0]:.1f} - {meas_result['x_top_border'][1]:.1f})")
        print(
            f"    底部宽度: {meas_result['width_x_bottom']:.2f} μm (边界: {meas_result['x_bottom_border'][0]:.1f} - {meas_result['x_bottom_border'][1]:.1f})")
        print(f"  Y方向:")
        print(
            f"    顶部宽度: {meas_result['width_y_top']:.2f} μm (边界: {meas_result['y_top_border'][0]:.1f} - {meas_result['y_top_border'][1]:.1f})")
        print(
            f"    底部宽度: {meas_result['width_y_bottom']:.2f} μm (边界: {meas_result['y_bottom_border'][0]:.1f} - {meas_result['y_bottom_border'][1]:.1f})")
        print("-" * 50)
