import numpy as np
from utils.pillars_detector import PillarInfo
from typing import TypedDict


class MeasResult(TypedDict):
    height: float
    width_x_top: float
    width_y_top: float
    width_x_bottom: float
    width_y_bottom: float
    x_max_height: float
    x_top_height: float
    x_bottom_height: float
    y_max_height: float
    y_top_height: float
    y_bottom_height: float
    x_top_border: tuple[float, float]
    y_top_border: tuple[float, float]
    x_bottom_border: tuple[float, float]
    y_bottom_border: tuple[float, float]


def measure_pillar(pillar_info: PillarInfo, calibrated_matrix: np.ndarray,
                   x_step: float, y_step: float) -> MeasResult:
    """
    使用十字截面法测量柱子尺寸

    Args:
        pillar_info: 柱子信息，包含中心坐标和边界框
        calibrated_matrix: 校准后的高度矩阵
        x_step: x方向像素间距(μm)
        y_step: y方向像素间距(μm)

    Returns:
        MeasResult: 包含各项测量结果的字典
    """

    def find_boundary_improved(coords, heights, threshold):
        """使用线性插值的边界查找方法"""
        valid_mask = heights >= threshold
        if not np.any(valid_mask):
            return None

        valid_indices = np.where(valid_mask)[0]
        min_idx = np.min(valid_indices)
        max_idx = np.max(valid_indices)

        # 左边界插值
        if min_idx > 0:  # 可以向外插值
            # 在threshold处进行线性插值
            h1, h2 = heights[min_idx - 1], heights[min_idx]
            c1, c2 = coords[min_idx - 1], coords[min_idx]

            if h1 != h2:  # 避免除零
                # 线性插值找到threshold对应的坐标
                left_boundary = c1 + (threshold - h1) * (c2 - c1) / (h2 - h1)
            else:
                left_boundary = (c1 + c2) / 2  # 退化为均值
        else:
            left_boundary = coords[min_idx]

        # 右边界插值
        if max_idx < len(coords) - 1:  # 可以向外插值
            # 在threshold处进行线性插值
            h1, h2 = heights[max_idx], heights[max_idx + 1]
            c1, c2 = coords[max_idx], coords[max_idx + 1]

            if h1 != h2:  # 避免除零
                # 线性插值找到threshold对应的坐标
                right_boundary = c1 + (threshold - h1) * (c2 - c1) / (h2 - h1)
            else:
                right_boundary = (c1 + c2) / 2  # 退化为均值
        else:
            right_boundary = coords[max_idx]

        return left_boundary, right_boundary

    def calculate_circle_average_height(matrix, center_row, center_col, radius_um, x_step, y_step):
        """
        计算以指定中心为圆心、指定半径的圆内的平均高度

        Args:
            matrix: 高度矩阵
            center_row, center_col: 圆心坐标（像素）
            radius_um: 圆的半径（微米）
            x_step, y_step: 像素间距（微米）

        Returns:
            圆内的平均高度
        """
        # 将半径从微米转换为像素
        radius_x_pixels = radius_um / x_step
        radius_y_pixels = radius_um / y_step

        # 确定搜索范围
        rows, cols = matrix.shape
        min_row = max(0, int(center_row - radius_y_pixels) - 1)
        max_row = min(rows - 1, int(center_row + radius_y_pixels) + 1)
        min_col = max(0, int(center_col - radius_x_pixels) - 1)
        max_col = min(cols - 1, int(center_col + radius_x_pixels) + 1)

        heights_in_circle = []

        # 遍历可能在圆内的像素
        for r in range(min_row, max_row + 1):
            for c in range(min_col, max_col + 1):
                # 计算物理距离
                dx_um = (c - center_col) * x_step
                dy_um = (r - center_row) * y_step
                distance_um = np.sqrt(dx_um ** 2 + dy_um ** 2)

                # 如果在圆内，添加到列表
                if distance_um <= radius_um:
                    heights_in_circle.append(matrix[r, c])

        # 计算平均高度
        if len(heights_in_circle) > 0:
            return np.mean(heights_in_circle)
        else:
            # 如果圆内没有像素，返回中心点的高度
            return matrix[center_row, center_col]

    # 提取柱子信息
    center_row, center_col = pillar_info['center']
    bbox = pillar_info['bbox']
    min_row, min_col, max_row, max_col = bbox

    # 提取柱子区域的高度数据
    pillar_region = calibrated_matrix[min_row:max_row + 1, min_col:max_col + 1]

    # 计算圆内平均高度（直径3μm，半径1.5μm）
    radius_um = 1.5  # 直径3μm的半径
    height = float(calculate_circle_average_height(calibrated_matrix, center_row, center_col,
                                                   radius_um, x_step, y_step))

    # 提取十字截面数据
    # X截面（通过中心行）
    x_slice = calibrated_matrix[center_row, min_col:max_col + 1]
    x_coords = np.arange(min_col, max_col + 1, dtype=float)

    # 定义X方向顶部和底部的高度
    x_max_height = np.max(x_slice)
    x_top_threshold = 0.95 * x_max_height
    x_bottom_threshold = 0.10 * x_max_height

    # Y截面（通过中心列）
    y_slice = calibrated_matrix[min_row:max_row + 1, center_col]
    y_coords = np.arange(min_row, max_row + 1, dtype=float)

    # 定义Y方向顶部和底部的高度
    y_max_height = np.max(y_slice)
    y_top_threshold = 0.95 * y_max_height
    y_bottom_threshold = 0.10 * y_max_height

    # 计算顶部宽度
    x_top_result = find_boundary_improved(x_coords, x_slice, x_top_threshold)
    y_top_result = find_boundary_improved(y_coords, y_slice, y_top_threshold)

    if x_top_result is None:
        x_top_border = (float(center_col), float(center_col))
        width_x_top = 0.0
    else:
        x_top_border = x_top_result
        width_x_top = (x_top_border[1] - x_top_border[0]) * x_step

    if y_top_result is None:
        y_top_border = (float(center_row), float(center_row))
        width_y_top = 0.0
    else:
        y_top_border = y_top_result
        width_y_top = (y_top_border[1] - y_top_border[0]) * y_step

    # 计算底部宽度
    x_bottom_result = find_boundary_improved(x_coords, x_slice, x_bottom_threshold)
    y_bottom_result = find_boundary_improved(y_coords, y_slice, y_bottom_threshold)

    if x_bottom_result is None:
        x_bottom_border = (float(center_col), float(center_col))
        width_x_bottom = 0.0
    else:
        x_bottom_border = x_bottom_result
        width_x_bottom = (x_bottom_border[1] - x_bottom_border[0]) * x_step

    if y_bottom_result is None:
        y_bottom_border = (float(center_row), float(center_row))
        width_y_bottom = 0.0
    else:
        y_bottom_border = y_bottom_result
        width_y_bottom = (y_bottom_border[1] - y_bottom_border[0]) * y_step

    # 构建返回结果
    result: MeasResult = {
        'height': height,
        'width_x_top': width_x_top,
        'width_y_top': width_y_top,
        'width_x_bottom': width_x_bottom,
        'width_y_bottom': width_y_bottom,
        'x_max_height': x_max_height,
        'x_top_height': x_top_threshold,
        'x_bottom_height': x_bottom_threshold,
        'y_max_height': y_max_height,
        'y_top_height': y_top_threshold,
        'y_bottom_height': y_bottom_threshold,
        'x_top_border': x_top_border,
        'y_top_border': y_top_border,
        'x_bottom_border': x_bottom_border,
        'y_bottom_border': y_bottom_border
    }

    return result