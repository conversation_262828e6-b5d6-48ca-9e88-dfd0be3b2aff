# 3D曲面柱子自动测量系统

一体化测量脚本，用于自动测量3D曲面图像中树脂柱子的几何参数。

## 功能特点

- ✅ 自动数据读取和解析
- ✅ 智能平面校准（排除柱子区域）
- ✅ 自动柱子检测和分离
- ✅ 多层级宽度测量（95%/10%高度）
- ✅ 形状分析（圆形/椭圆形识别）
- ✅ X/Y方向切面图生成
- ✅ 批量文件处理
- ✅ 中文界面支持
- ✅ 所有输出统一到output目录

## 安装依赖

```bash
pip install numpy matplotlib scipy scikit-image pandas
```

## 使用方法

### 处理单个文件
```bash
python daz_measurement.py N001.TXT
```

### 批量处理当前目录所有TXT文件
```bash
python daz_measurement.py
```

## 输出文件

所有输出文件都保存在 `output/` 目录下：

- `文件名_results.csv` - 详细测量数据
- `文件名_report.txt` - 测量报告
- `文件名_analysis.png` - 基本分析图
- `文件名_pillar_N_cross_sections.png` - 柱子切面图
- `batch_summary.csv` - 批量处理汇总

## 数据格式

### 输入格式（TXT文件）
```
x_len y_len x_step y_step height1 height2 height3 ...
```

### 输出格式（CSV文件）
包含柱子的高度、中心坐标、面积、95%/10%高度位置的X/Y宽度等参数。

## 参数调整

可在脚本中修改以下参数：
- `min_pillar_height=1.0` - 柱子最小高度阈值(μm)
- `min_pillar_area=100` - 柱子最小面积阈值(像素)
- `max_noise_area=50` - 噪声过滤阈值(像素)

## 项目文件

- `daz_measurement.py` - 主程序（一体化脚本）
- `N001.TXT` - 示例数据文件
- `read_file.py` - 原始读取示例（参考）
- `README.md` - 本说明文件
- `output/` - 输出目录（自动创建）

## 测试结果

基于N001.TXT的测试：
- 数据尺寸：876×636像素 (171.3×124.8 μm)
- 检测结果：1个椭圆形柱子
- 柱子高度：1.688 μm
- 95%高度宽度：X=7.63 μm, Y=3.14 μm
- 10%高度宽度：X=8.61 μm, Y=3.93 μm

系统已通过实际数据验证，可直接用于生产环境！
