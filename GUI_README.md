# DAZ自动测量系统 - GUI版本

基于PySide6的桌面应用，实现DAZ文件的可视化测量流程。

## 功能特点

✅ **拖拽式文件加载** - 支持拖拽.daz/.da6文件到界面  
✅ **自动化流程** - 文件解析→平面校准→柱子检测→量测一体化  
✅ **交互式可视化** - 实时显示检测结果和量测数据  
✅ **便捷操作** - 点击选择柱子，方向键切换，找平结果弹窗查看  
✅ **参数调节** - 可调整最小柱高参数重新检测  

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 文件路径显示区域          │ 最小柱高设置 │ 查看找平结果按钮 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │             │  │             │  │             │        │
│  │   柱子检测   │  │   X方向     │  │   Y方向     │        │
│  │   可视化     │  │   切面图    │  │   切面图    │        │
│  │             │  │             │  │             │        │
│  │  (点击选择)  │  │ (量测结果)  │  │ (量测结果)  │        │
│  │             │  │             │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## 使用方法

### 1. 启动应用
```bash
python run_gui.py
```

### 2. 加载文件
- **方法1**: 直接拖拽.daz或.da6文件到界面任意位置
- **方法2**: 应用会自动解析文件并执行后续流程

### 3. 查看结果
- **柱子检测**: 左侧显示校准后矩阵和检测到的柱子
- **切面分析**: 中间和右侧显示X/Y方向的切面图和量测数据
- **找平结果**: 点击"查看找平结果"按钮查看详细校准信息

### 4. 交互操作
- **选择柱子**: 在左侧检测图中点击柱子边界框
- **切换柱子**: 使用方向键(←→↑↓)快速切换
- **调整参数**: 修改最小柱高数值会自动重新检测

## 技术架构

采用**Model-View模式**，利用Qt信号槽实现松耦合：

- **DataModel**: 单一数据源，管理所有状态
- **MainWindow**: 主界面布局和交互逻辑  
- **PillarDetectionCanvas**: 柱子检测可视化组件
- **CrossSectionCanvas**: 切面图可视化组件
- **CalibrationDialog**: 找平结果弹窗

## 依赖要求

```bash
pip install PySide6 numpy matplotlib scikit-image scikit-learn pandas
```

## 性能优化

- 支持1000x1000像素矩阵的实时可视化
- 智能缓存机制，避免重复计算
- 响应式界面更新，确保流畅交互

## 快捷键

- `←/↑`: 切换到上一个柱子
- `→/↓`: 切换到下一个柱子
- `拖拽`: 加载新文件

## 故障排除

1. **应用无法启动**: 检查PySide6是否正确安装
2. **文件无法加载**: 确认文件格式为.daz或.da6
3. **可视化异常**: 检查matplotlib版本兼容性
4. **性能问题**: 对于超大文件，建议关闭其他占用内存的程序

---

**设计理念**: 遵循Linus Torvalds的"好品味"原则 - 简洁、实用、无特殊情况的设计。
