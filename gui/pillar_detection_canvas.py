#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
柱子检测可视化组件
显示校准后矩阵和柱子检测结果，支持点击选择柱子
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel
from PySide6.QtCore import Qt

from gui.data_model import DataModel

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PillarDetectionCanvas(QWidget):
    """
    柱子检测可视化组件
    
    功能：
    1. 显示校准后的高度矩阵热力图
    2. 叠加柱子检测结果（边界框、中心点、标签）
    3. 高亮当前选中的柱子
    4. 支持点击选择柱子
    """
    
    def __init__(self, data_model: DataModel):
        super().__init__()
        self.data_model = data_model
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形，使用constrained_layout
        self.figure = Figure(figsize=(8, 8), constrained_layout=True)
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        # 不再手动管理colorbar，让matplotlib自己处理
        
        layout.addWidget(self.canvas)
        
        # 连接鼠标点击事件
        self.canvas.mpl_connect('button_press_event', self._on_mouse_click)
        
        # 初始化显示
        self._show_empty_plot()
    
    def _connect_signals(self):
        """连接数据模型信号"""
        self.data_model.plane_calibrated.connect(self._update_plot)
        self.data_model.pillars_detected.connect(self._update_plot)
        self.data_model.current_pillar_changed.connect(self._update_plot)
    
    def _show_empty_plot(self):
        """显示空白图"""
        self.ax.clear()
        self.ax.text(0.5, 0.5, '请拖入DAZ文件', 
                    transform=self.ax.transAxes, 
                    ha='center', va='center',
                    fontsize=16, color='gray')
        self.ax.set_xticks([])
        self.ax.set_yticks([])
        self.canvas.draw()
    
    def _update_plot(self):
        """更新图形显示"""
        if not self.data_model.is_plane_calibrated():
            self._show_empty_plot()
            return

        # 清除整个figure，重新创建subplot - 简单粗暴但有效
        self.figure.clear()
        self.ax = self.figure.add_subplot(111)

        # 获取数据
        calibrated_matrix = self.data_model.calibrated_matrix
        x_step = self.data_model.x_step
        y_step = self.data_model.y_step
        pillars_info = self.data_model.pillars_info
        current_pillar_index = self.data_model.current_pillar_index

        # 计算物理坐标范围
        height, width = calibrated_matrix.shape
        x_extent = [0, width * x_step]  # 物理X坐标范围 (μm)
        y_extent = [0, height * y_step]  # 物理Y坐标范围 (μm)

        # 绘制校准后矩阵热力图（使用物理坐标）
        im = self.ax.imshow(calibrated_matrix, cmap='viridis',
                           aspect='equal', origin='lower',
                           extent=[x_extent[0], x_extent[1], y_extent[0], y_extent[1]])

        # 设置标题和标签
        self.ax.set_title('校准后矩阵 + 柱子检测结果')
        self.ax.set_xlabel('X方向 (μm)')
        self.ax.set_ylabel('Y方向 (μm)')

        # 添加colorbar - 不保存引用，让matplotlib管理
        self.figure.colorbar(im, ax=self.ax, label='校准后高度 (μm)')

        # 绘制柱子检测结果
        if pillars_info:
            self._draw_pillars(pillars_info, current_pillar_index, x_step, y_step)

        # 刷新画布 - constrained_layout自动处理布局
        self.canvas.draw()
    
    def _draw_pillars(self, pillars_info, current_pillar_index, x_step, y_step):
        """绘制柱子检测结果（使用物理坐标）"""
        for i, pillar in enumerate(pillars_info):
            center_row, center_col = pillar['center']
            bbox = pillar['bbox']
            pillar_id = pillar['pillar_id']

            # 转换为物理坐标
            center_x = center_col * x_step
            center_y = center_row * y_step

            # 边界框物理坐标
            min_row, min_col, max_row, max_col = bbox
            min_x = min_col * x_step
            min_y = min_row * y_step
            width_phys = (max_col - min_col) * x_step
            height_phys = (max_row - min_row) * y_step

            # 判断是否为当前选中柱子
            is_current = (i == current_pillar_index)

            # 设置颜色和样式
            if is_current:
                edge_color = 'red'
                line_width = 3
                alpha = 1.0
                marker_size = 12
                font_weight = 'bold'
                font_size = 12
            else:
                edge_color = 'yellow'
                line_width = 1
                alpha = 0.7
                marker_size = 8
                font_weight = 'normal'
                font_size = 10

            # 绘制边界框（物理坐标）
            rect = patches.Rectangle(
                (min_x, min_y), width_phys, height_phys,
                linewidth=line_width, edgecolor=edge_color,
                facecolor='none', alpha=alpha
            )
            self.ax.add_patch(rect)

            # 绘制中心点（物理坐标）
            self.ax.plot(center_x, center_y, '+',
                        color=edge_color, markersize=marker_size,
                        markeredgewidth=2, alpha=alpha)

            # 标注柱子ID（物理坐标）- 改善可读性
            if is_current:
                text_color = 'white'
                bg_color = 'red'
                bg_alpha = 0.9
            else:
                text_color = 'black'
                bg_color = 'yellow'
                bg_alpha = 0.9

            self.ax.annotate(
                str(pillar_id),
                (center_x, center_y),
                xytext=(10, 10), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.4',
                         facecolor=bg_color, alpha=bg_alpha,
                         edgecolor='black', linewidth=1),
                fontsize=font_size, color=text_color,
                weight=font_weight
            )

            # 如果是当前柱子，绘制十字线（物理坐标）
            if is_current:
                self.ax.axhline(y=center_y, color='red',
                               linestyle='--', alpha=0.6, linewidth=1)
                self.ax.axvline(x=center_x, color='red',
                               linestyle='--', alpha=0.6, linewidth=1)
    
    def _on_mouse_click(self, event):
        """鼠标点击事件处理"""
        if event.inaxes != self.ax:
            return

        if not self.data_model.is_pillars_detected():
            return

        # 获取点击位置（物理坐标）
        x_phys = event.xdata
        y_phys = event.ydata

        # 转换为数组坐标
        x_step = self.data_model.x_step
        y_step = self.data_model.y_step
        col = int(round(x_phys / x_step))
        row = int(round(y_phys / y_step))

        # 检查点击位置是否在某个柱子的bbox内
        if self.data_model.select_pillar_by_position(row, col):
            # 成功选中柱子，图形会通过信号自动更新
            pass
