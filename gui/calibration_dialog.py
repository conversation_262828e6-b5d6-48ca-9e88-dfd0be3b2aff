#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
校准对话框 - 显示找平可视化结果
展示找平前后对比和mask
"""

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel

from gui.data_model import DataModel

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CalibrationDialog(QDialog):
    """
    校准结果对话框
    
    显示三个图：
    1. 找平前热力图
    2. 平面点mask
    3. 找平后热力图
    """
    
    def __init__(self, data_model: DataModel, parent=None):
        super().__init__(parent)
        self.data_model = data_model
        
        self.setWindowTitle("平面校准结果")
        self.setModal(True)
        self.resize(1200, 400)
        
        self._setup_ui()
        self._plot_calibration_results()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(15, 5))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 统计信息标签
        self.stats_label = QLabel("")
        self.stats_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: monospace;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.stats_label)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
    
    def _plot_calibration_results(self):
        """绘制校准结果"""
        if not self.data_model.is_plane_calibrated():
            return
        
        # 获取数据
        height_matrix = self.data_model.height_matrix
        calibrated_matrix = self.data_model.calibrated_matrix
        metadata = self.data_model.calibration_metadata
        x_step = self.data_model.x_step
        y_step = self.data_model.y_step
        
        # 清除之前的图
        self.figure.clear()
        
        # 创建三个子图，手动调整间距
        ax1 = self.figure.add_subplot(131)
        ax2 = self.figure.add_subplot(132)
        ax3 = self.figure.add_subplot(133)

        # 调整subplot间距，避免重叠和过大间隙
        self.figure.subplots_adjust(left=0.05, right=0.95, wspace=0.4)
        
        # 计算物理坐标范围
        height, width = height_matrix.shape
        x_extent = [0, width * x_step]  # 物理X坐标范围 (μm)
        y_extent = [0, height * y_step]  # 物理Y坐标范围 (μm)

        # === 左图：找平前热力图 ===
        im1 = ax1.imshow(height_matrix, cmap='viridis', aspect='equal', origin='lower',
                        extent=(x_extent[0], x_extent[1], y_extent[0], y_extent[1]))
        ax1.set_title('找平前高度矩阵')
        ax1.set_xlabel('X方向 (μm)')
        ax1.set_ylabel('Y方向 (μm)')
        self.figure.colorbar(im1, ax=ax1, label='高度 (μm)', shrink=0.8)

        # === 中图：平面点mask ===
        inlier_mask = metadata['inlier_mask']
        # 使用离散颜色映射
        from matplotlib.colors import ListedColormap
        colors = ['#000000', '#4ecdc4']  # 黑色(非平面点), 青色(平面点)
        discrete_cmap = ListedColormap(colors)

        im2 = ax2.imshow(inlier_mask, cmap=discrete_cmap, aspect='equal', origin='lower',
                        extent=(x_extent[0], x_extent[1], y_extent[0], y_extent[1]),
                        vmin=0, vmax=1)
        ax2.set_title(f'平面点识别 (内点比例: {metadata["inlier_ratio"]:.1%})')
        ax2.set_xlabel('X方向 (μm)')
        ax2.set_ylabel('Y方向 (μm)')

        # 创建invisible colorbar保持尺寸一致，然后添加图例
        cbar2 = self.figure.colorbar(im2, ax=ax2, shrink=0.8)
        cbar2.ax.set_visible(False)  # 隐藏colorbar但保留空间

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='#000000', label='非平面点'),
            Patch(facecolor='#4ecdc4', label='平面点')
        ]
        ax2.legend(handles=legend_elements, loc='upper right', fontsize=10)

        # === 右图：找平后热力图 ===
        im3 = ax3.imshow(calibrated_matrix, cmap='viridis', aspect='equal', origin='lower',
                        extent=(x_extent[0], x_extent[1], y_extent[0], y_extent[1]))
        ax3.set_title('找平后高度矩阵')
        ax3.set_xlabel('X方向 (μm)')
        ax3.set_ylabel('Y方向 (μm)')
        self.figure.colorbar(im3, ax=ax3, label='校准后高度 (μm)', shrink=0.8)

        # 更新统计信息标签
        stats_text = (
            f"校准前标准差: {metadata['std_before']:.3f} μm    "
            f"校准后标准差: {metadata['std_after']:.3f} μm    "
            f"改善倍数: {metadata['std_before']/metadata['std_after']:.2f}x    "
            f"内点比例: {metadata['inlier_ratio']:.1%}"
        )
        self.stats_label.setText(stats_text)
        
        # 刷新画布 - 不使用tight_layout，已手动调整间距
        self.canvas.draw()
